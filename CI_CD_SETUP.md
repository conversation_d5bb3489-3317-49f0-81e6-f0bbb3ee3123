# CI/CD Setup Guide for AI React Frontend

## 📋 Overview

This document outlines the complete CI/CD pipeline setup for the AI React Frontend application. The pipeline includes building, testing, Docker image creation, and GitOps deployment.

## 🔐 Required GitHub Secrets

To run the CI/CD pipeline successfully, you need to configure the following GitHub repository secrets:

### Docker Hub Secrets
- **`DOCKERHUB_TOKEN`**: Your Docker Hub access token
- **`DOCKERHUB_USERNAME`**: Your Docker Hub username (should be `saipriya104`)

### GitOps Deployment Secret
- **`GITOPS_DEPLOY_TOKEN`**: GitHub token for triggering GitOps deployments

## 🏗️ Pipeline Jobs

### 1. Build Job
- **Purpose**: Builds the React application
- **Actions**:
  - Checkout code
  - Setup Node.js 18 with npm caching
  - Install dependencies (`npm ci`)
  - Run linting (`npm run lint`)
  - Build application (`npm run build:spring`)
  - Upload build artifacts

### 2. Test Job
- **Purpose**: Runs the test suite
- **Actions**:
  - Checkout code
  - Setup Node.js 18 with npm caching
  - Install dependencies (`npm ci`)
  - Run tests with coverage (`npm run test:coverage`)
  - Upload coverage reports

### 3. Docker Job
- **Purpose**: Builds and pushes Docker images
- **Dependencies**: Requires both build and test jobs to complete
- **Actions**:
  - Download build artifacts
  - Build Docker image with multi-stage Dockerfile
  - Push to Docker Hub repository: `saipriya104/ai-react-frontend`
  - Tag images with commit SHA and branch name

### 4. Deploy Job
- **Purpose**: Triggers GitOps deployment
- **Dependencies**: Requires docker job to complete
- **Actions**:
  - Triggers deployment to GitOps repository
  - Sends deployment payload with React app details

## 🐳 Docker Configuration

### Repository
- **Docker Hub Repository**: `saipriya104/ai-react-frontend`
- **Image Tags**: 
  - `latest` (for main branch)
  - `{branch-name}` (for feature branches)
  - `{commit-sha}` (for specific commits)

### Dockerfile Features
- Multi-stage build (Node.js build + Nginx serve)
- Optimized for React SPA
- Health checks included
- Security headers configured
- Gzip compression enabled
- Static asset caching

## 🚀 Deployment

### Trigger Conditions
- **Push Events**: `main` branch and `arc*` branches
- **Pull Requests**: `main` branch (build and test only)

### GitOps Integration
- **Target Repository**: `ChidhagniConsulting/gitops-argocd-apps`
- **Event Type**: `deploy-to-argocd`
- **Application**: `ai-react-frontend`

## 📝 Setup Instructions

1. **Configure Docker Hub Secrets**:
   ```
   DOCKERHUB_TOKEN=your_docker_hub_token
   DOCKERHUB_USERNAME=saipriya104
   ```

2. **Configure GitOps Secret**:
   ```
   GITOPS_DEPLOY_TOKEN=your_github_token_with_repo_access
   ```

3. **Verify Docker Hub Repository**:
   - Ensure `saipriya104/ai-react-frontend` repository exists
   - Verify push permissions

4. **Test Pipeline**:
   - Push to a feature branch starting with `arc`
   - Verify all jobs complete successfully
   - Check Docker Hub for pushed images

## 📁 File Structure

```
.
├── .github/workflows/ci.yml    # CI/CD pipeline
├── Dockerfile                  # Multi-stage Docker build
├── nginx.conf                  # Nginx configuration
├── .dockerignore              # Docker ignore file
└── CI_CD_SETUP.md             # This documentation
```

## 🔧 Troubleshooting

### Common Issues
1. **Docker login fails**: Check DOCKERHUB_TOKEN and DOCKERHUB_USERNAME secrets
2. **GitOps deployment fails**: Verify GITOPS_DEPLOY_TOKEN has correct permissions
3. **Build fails**: Check Node.js version and npm scripts in package.json
4. **Test fails**: Ensure all test dependencies are properly configured

### Performance Optimizations
- **Dependency Caching**: NPM dependencies cached between runs
- **Parallel Execution**: Build and test jobs run simultaneously
- **Artifact Sharing**: Build artifacts shared between jobs
- **Docker Layer Caching**: Multi-stage build optimizes layer caching

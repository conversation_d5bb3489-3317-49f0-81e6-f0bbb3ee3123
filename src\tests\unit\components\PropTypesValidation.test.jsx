import React from 'react';
import { render } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { AuthProvider } from '@/tests/contexts/AuthContext';
import AuthContainer from '@/tests/unit/AuthContainer';
import ForgotPasswordForm from '@/tests/unit/ForgotPasswordForm';
import LoginForm from '@/tests/unit/LoginForm';
import ProtectedRoute from '@/tests/unit/ProtectedRoute';

// Mock console.error to capture PropTypes warnings
const originalError = console.error;
let consoleErrorSpy;

beforeEach(() => {
  consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
});

afterEach(() => {
  consoleErrorSpy.mockRestore();
});

jest.mock('@/tests/config/env', () => ({
  getApiBaseUrl: () => 'http://localhost:8080',
}));

const renderWithProviders = (component) => {
  return render(
    <BrowserRouter>
      <AuthProvider>
        {component}
      </AuthProvider>
    </BrowserRouter>
  );
};

describe('PropTypes Validation', () => {
  describe('AuthContainer', () => {
    it('should not show PropTypes warnings with valid props', () => {
      renderWithProviders(<AuthContainer initialView="login" />);
      expect(consoleErrorSpy).not.toHaveBeenCalled();
    });

    it('should not show PropTypes warnings with default props', () => {
      renderWithProviders(<AuthContainer />);
      expect(consoleErrorSpy).not.toHaveBeenCalled();
    });

    it('should show PropTypes warning with invalid initialView', () => {
      renderWithProviders(<AuthContainer initialView="invalid" />);
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining('Warning: Failed prop type')
      );
    });
  });

  describe('ForgotPasswordForm', () => {
    it('should not show PropTypes warnings with valid props', () => {
      const mockOnSwitchToLogin = jest.fn();
      renderWithProviders(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);
      expect(consoleErrorSpy).not.toHaveBeenCalled();
    });

    it('should show PropTypes warning when onSwitchToLogin is missing', () => {
      renderWithProviders(<ForgotPasswordForm />);
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining('Warning: Failed prop type')
      );
    });

    it('should show PropTypes warning when onSwitchToLogin is not a function', () => {
      renderWithProviders(<ForgotPasswordForm onSwitchToLogin="not-a-function" />);
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining('Warning: Failed prop type')
      );
    });
  });

  describe('LoginForm', () => {
    it('should not show PropTypes warnings with valid props', () => {
      const mockOnSwitchToSignUp = jest.fn();
      const mockOnSwitchToForgotPassword = jest.fn();
      renderWithProviders(
        <LoginForm 
          onSwitchToSignUp={mockOnSwitchToSignUp}
          onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
        />
      );
      expect(consoleErrorSpy).not.toHaveBeenCalled();
    });

    it('should show PropTypes warning when onSwitchToSignUp is missing', () => {
      const mockOnSwitchToForgotPassword = jest.fn();
      renderWithProviders(
        <LoginForm onSwitchToForgotPassword={mockOnSwitchToForgotPassword} />
      );
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining('Warning: Failed prop type')
      );
    });

    it('should show PropTypes warning when onSwitchToForgotPassword is missing', () => {
      const mockOnSwitchToSignUp = jest.fn();
      renderWithProviders(
        <LoginForm onSwitchToSignUp={mockOnSwitchToSignUp} />
      );
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining('Warning: Failed prop type')
      );
    });
  });

  describe('ProtectedRoute', () => {
    it('should not show PropTypes warnings with valid children', () => {
      renderWithProviders(
        <ProtectedRoute>
          <div>Protected content</div>
        </ProtectedRoute>
      );
      expect(consoleErrorSpy).not.toHaveBeenCalled();
    });

    it('should show PropTypes warning when children is missing', () => {
      renderWithProviders(<ProtectedRoute />);
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining('Warning: Failed prop type')
      );
    });
  });
});

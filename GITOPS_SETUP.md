# GitOps Setup Instructions

## Issue
The CI/CD pipeline successfully builds and pushes Docker images, but no ArgoCD application is created because the GitOps repository (`gitops-argocd-apps`) doesn't have the necessary workflow to handle deployment requests.

## Solution
The `gitops-argocd-apps` repository needs a workflow file to handle the `deploy-to-argocd` repository dispatch event.

## Required Files in GitOps Repository

### 1. Workflow File
**Location**: `gitops-argocd-apps/.github/workflows/deploy-to-argocd.yml`

This workflow will:
- Listen for `deploy-to-argocd` repository dispatch events
- Generate ArgoCD Application manifests
- Create Kubernetes deployment manifests
- Commit changes to the GitOps repository

### 2. Directory Structure (Auto-generated)
```
gitops-argocd-apps/
├── applications/
│   └── dev/
│       └── ai-react-frontend/
│           ├── application.yaml          # ArgoCD Application
│           ├── manifests/
│           │   ├── namespace.yaml
│           │   ├── deployment.yaml
│           │   ├── service.yaml
│           │   ├── ingress.yaml
│           │   └── kustomization.yaml
│           └── README.md
```

## Current Repository Structure
This repository (`ai-react-frontend`) should only contain:
- ✅ Application source code
- ✅ Dockerfile
- ✅ CI/CD workflow (`.github/workflows/ci.yml`)
- ✅ Self-hosted runner config (`k8s/arc/runner-deployment.yaml`)
- ❌ ~~Kubernetes application manifests~~ (removed - belongs in GitOps repo)
- ❌ ~~ArgoCD application templates~~ (removed - belongs in GitOps repo)

## Next Steps
1. **In the `gitops-argocd-apps` repository**, create the workflow file
2. **Trigger a new deployment** by pushing to this repository
3. **Verify** that the GitOps workflow runs and creates the necessary files
4. **Check ArgoCD** for the new application

## Manual Workaround (if needed)
If the GitOps automation isn't working immediately, you can manually create an ArgoCD application:

```bash
kubectl apply -f - <<EOF
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ai-react-frontend-dev
  namespace: argocd
spec:
  project: default
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps.git
    targetRevision: HEAD
    path: applications/dev/ai-react-frontend/manifests
  destination:
    server: https://kubernetes.default.svc
    namespace: ai-react-frontend-dev
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
EOF
```

## Repository Separation Benefits
- **Clean separation of concerns**: Application code vs deployment configuration
- **Security**: GitOps repository can have different access controls
- **Flexibility**: Multiple applications can be managed in the same GitOps repository
- **Audit trail**: Deployment changes are tracked separately from code changes

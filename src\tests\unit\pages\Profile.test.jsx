import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import Profile from '@/pages/Profile';

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock MUI components
jest.mock('@mui/material', () => ({
  Container: ({ children, maxWidth, sx, ...props }) => (
    <div data-testid="container" data-maxwidth={maxWidth} {...props}>{children}</div>
  ),
  Paper: ({ children, elevation, sx, ...props }) => (
    <div data-testid="paper" data-elevation={elevation} {...props}>{children}</div>
  ),
  Typography: ({ children, variant, component, gutterBottom, sx, ...props }) => (
    <div data-testid="typography" data-variant={variant} data-component={component} {...props}>
      {children}
    </div>
  ),
  Avatar: ({ children, src, sx, ...props }) => (
    <div data-testid="avatar" data-src={src} {...props}>{children}</div>
  ),
  TextField: ({ label, value, onChange, fullWidth, margin, variant, type, disabled, InputProps, ...props }) => (
    <input
      data-testid="text-field"
      data-label={label}
      value={value}
      onChange={onChange}
      data-fullwidth={fullWidth}
      data-margin={margin}
      data-variant={variant}
      type={type}
      disabled={disabled}
      placeholder={label}
      {...props}
    />
  ),
  Button: ({ children, variant, color, fullWidth, sx, onClick, disabled, size, ...props }) => (
    <button
      data-testid="button"
      data-variant={variant}
      data-color={color}
      data-fullwidth={fullWidth}
      data-size={size}
      onClick={onClick}
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  ),
  Box: ({ children, component, sx, ...props }) => {
    const Component = component || 'div';
    return <Component data-testid="box" {...props}>{children}</Component>;
  },
  IconButton: ({ children, onClick, sx, edge, ...props }) => (
    <button data-testid="icon-button" onClick={onClick} data-edge={edge} {...props}>
      {children}
    </button>
  ),
  Grid: ({ children, container, item, xs, sm, md, spacing, ...props }) => (
    <div
      data-testid="grid"
      data-container={container}
      data-item={item}
      data-xs={xs}
      data-sm={sm}
      data-md={md}
      data-spacing={spacing}
      {...props}
    >
      {children}
    </div>
  ),
  Card: ({ children, sx, ...props }) => (
    <div data-testid="card" {...props}>{children}</div>
  ),
  CardContent: ({ children, sx, ...props }) => (
    <div data-testid="card-content" {...props}>{children}</div>
  ),
  Divider: ({ sx, ...props }) => (
    <div data-testid="divider" {...props} />
  ),
  AppBar: ({ children, position, sx, ...props }) => (
    <div data-testid="app-bar" data-position={position} {...props}>{children}</div>
  ),
  Toolbar: ({ children, ...props }) => (
    <div data-testid="toolbar" {...props}>{children}</div>
  ),
}));

// Mock Lucide React icons
jest.mock('lucide-react', () => ({
  ArrowLeft: () => <div data-testid="arrow-left-icon" />,
  User: () => <div data-testid="user-icon" />,
  Mail: () => <div data-testid="mail-icon" />,
  Phone: () => <div data-testid="phone-icon" />,
  Calendar: () => <div data-testid="calendar-icon" />,
}));

jest.mock('@/config/env', () => ({
  getApiBaseUrl: () => 'http://localhost:8080',
}));

describe('Profile Page', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockNavigate.mockClear();
  });

  const renderWithRouter = (component) => {
    return render(
      <BrowserRouter>
        {component}
      </BrowserRouter>
    );
  };

  describe('Initial State', () => {
    test('should render with default user data', () => {
      // Act
      renderWithRouter(<Profile />);

      // Assert
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByDisplayValue('+****************')).toBeInTheDocument();
    });

    test('should start in view mode', () => {
      // Act
      renderWithRouter(<Profile />);

      // Assert
      expect(screen.getByText('Edit Profile')).toBeInTheDocument();
      expect(screen.queryByText('Save Changes')).not.toBeInTheDocument();
      expect(screen.queryByText('Cancel')).not.toBeInTheDocument();
    });
  });

  describe('Component Rendering', () => {
    test('should render profile settings title', () => {
      // Act
      renderWithRouter(<Profile />);

      // Assert
      expect(screen.getByText('Profile Settings')).toBeInTheDocument();
    });

    test('should render back button', () => {
      // Act
      renderWithRouter(<Profile />);

      // Assert
      expect(screen.getByTestId('arrow-left-icon')).toBeInTheDocument();
    });

    test('should render user avatar with initials', () => {
      // Act
      renderWithRouter(<Profile />);

      // Assert
      const avatar = screen.getByTestId('avatar');
      expect(avatar).toBeInTheDocument();
      expect(avatar).toHaveTextContent('JD'); // John Doe initials
    });

    test('should render user information in view mode', () => {
      // Act
      renderWithRouter(<Profile />);

      // Assert
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('Edit Profile')).toBeInTheDocument();
    });

    test('should render personal information section', () => {
      // Act
      renderWithRouter(<Profile />);

      // Assert
      expect(screen.getByText('Personal Information')).toBeInTheDocument();
    });

    test('should render member since date', () => {
      // Act
      renderWithRouter(<Profile />);

      // Assert
      expect(screen.getByText(/Member since/)).toBeInTheDocument();
    });
  });

  describe('Edit Mode Toggle', () => {
    test('should enter edit mode when edit button is clicked', async () => {
      // Arrange
      const user = userEvent.setup();
      renderWithRouter(<Profile />);

      // Act
      const editButton = screen.getByText('Edit Profile');
      await user.click(editButton);

      // Assert
      expect(screen.getByDisplayValue('John Doe')).toBeInTheDocument();
      expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('Save')).toBeInTheDocument();
      expect(screen.getByText('Cancel')).toBeInTheDocument();
    });

    test('should exit edit mode when cancel button is clicked', async () => {
      // Arrange
      const user = userEvent.setup();
      renderWithRouter(<Profile />);

      // Act - Enter edit mode
      const editButton = screen.getByText('Edit Profile');
      await user.click(editButton);

      // Act - Cancel edit mode
      const cancelButton = screen.getByText('Cancel');
      await user.click(cancelButton);

      // Assert
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('Edit Profile')).toBeInTheDocument();
    });

    test('should reset form values when canceling edit', async () => {
      // Arrange
      const user = userEvent.setup();
      renderWithRouter(<Profile />);

      // Act - Enter edit mode and modify values
      const editButton = screen.getByText('Edit Profile');
      await user.click(editButton);

      const nameField = screen.getByDisplayValue('John Doe');
      await user.clear(nameField);
      await user.type(nameField, 'Modified Name');

      // Act - Cancel edit mode
      const cancelButton = screen.getByText('Cancel');
      await user.click(cancelButton);

      // Act - Re-enter edit mode
      const editButtonAgain = screen.getByText('Edit Profile');
      await user.click(editButtonAgain);

      // Assert - Values should be reset to original
      expect(screen.getByDisplayValue('John Doe')).toBeInTheDocument();
    });
  });

  describe('Form Handling', () => {
    test('should update form values when typing', async () => {
      // Arrange
      const user = userEvent.setup();
      renderWithRouter(<Profile />);

      // Act - Enter edit mode
      const editButton = screen.getByText('Edit Profile');
      await user.click(editButton);

      // Act - Modify name field
      const nameField = screen.getByDisplayValue('John Doe');
      await user.clear(nameField);
      await user.type(nameField, 'Jane Smith');

      // Assert
      expect(screen.getByDisplayValue('Jane Smith')).toBeInTheDocument();
    });

    test('should handle email field changes', async () => {
      // Arrange
      const user = userEvent.setup();
      renderWithRouter(<Profile />);

      // Act - Enter edit mode
      const editButton = screen.getByText('Edit Profile');
      await user.click(editButton);

      // Act - Modify email field
      const emailField = screen.getByDisplayValue('<EMAIL>');
      await user.clear(emailField);
      await user.type(emailField, '<EMAIL>');

      // Assert
      expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
    });

    test('should save changes when save button is clicked', async () => {
      // Arrange
      const user = userEvent.setup();
      const { toast } = require('sonner');
      renderWithRouter(<Profile />);

      // Act - Enter edit mode and modify values
      const editButton = screen.getByText('Edit Profile');
      await user.click(editButton);

      const nameField = screen.getByDisplayValue('John Doe');
      await user.clear(nameField);
      await user.type(nameField, 'Jane Smith');

      // Act - Save changes
      const saveButton = screen.getByText('Save');
      await user.click(saveButton);

      // Assert - Should show loading state initially
      expect(screen.getByText('Saving...')).toBeInTheDocument();

      // Wait for save to complete
      await waitFor(() => {
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      });

      // Assert - Should exit edit mode and show updated values
      expect(screen.getByText('Edit Profile')).toBeInTheDocument();
      expect(toast.success).toHaveBeenCalledWith('Profile updated successfully');
    });

    test('should show loading state during save', async () => {
      // Arrange
      const user = userEvent.setup();
      renderWithRouter(<Profile />);

      // Act - Enter edit mode
      const editButton = screen.getByText('Edit Profile');
      await user.click(editButton);

      // Act - Save changes
      const saveButton = screen.getByText('Save');
      await user.click(saveButton);

      // Assert - Should show loading state
      expect(screen.getByText('Saving...')).toBeInTheDocument();
    });


  });

  describe('Navigation', () => {
    test('should navigate to dashboard when back button is clicked', async () => {
      // Arrange
      const user = userEvent.setup();
      renderWithRouter(<Profile />);

      // Act
      const backButton = screen.getByTestId('arrow-left-icon').closest('button');
      await user.click(backButton);

      // Assert
      expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
    });
  });

  describe('Component Structure', () => {
    test('should render with proper container structure', () => {
      // Act
      renderWithRouter(<Profile />);

      // Assert
      expect(screen.getByTestId('container')).toBeInTheDocument();
      expect(screen.getAllByTestId('card')).toHaveLength(2);
    });

    test('should render form fields with correct properties', async () => {
      // Arrange
      const user = userEvent.setup();
      renderWithRouter(<Profile />);

      // Act - Enter edit mode
      const editButton = screen.getByText('Edit Profile');
      await user.click(editButton);

      // Assert
      const textFields = screen.getAllByTestId('text-field');
      expect(textFields).toHaveLength(4);

      const nameField = textFields.find(field => field.getAttribute('data-label') === 'Full Name');
      const emailField = textFields.find(field => field.getAttribute('data-label') === 'Email Address');
      const phoneField = textFields.find(field => field.getAttribute('data-label') === 'Phone Number');
      const joinDateField = textFields.find(field => field.getAttribute('data-label') === 'Join Date');

      expect(nameField).toHaveAttribute('data-fullwidth', 'true');
      expect(emailField).toHaveAttribute('data-fullwidth', 'true');
      expect(phoneField).toHaveAttribute('data-fullwidth', 'true');
      expect(joinDateField).toHaveAttribute('data-fullwidth', 'true');
      expect(joinDateField).toHaveAttribute('disabled', '');
    });
  });

  describe('Error Handling', () => {
    test('should render without crashing', () => {
      // Act & Assert - Should not throw
      expect(() => renderWithRouter(<Profile />)).not.toThrow();
    });

    test('should handle component unmounting gracefully', () => {
      // Act
      const { unmount } = renderWithRouter(<Profile />);

      // Assert - Should not throw on unmount
      expect(() => unmount()).not.toThrow();
    });

    test('should handle multiple re-renders', () => {
      // Act
      const { rerender } = renderWithRouter(<Profile />);

      // Assert - Should render correctly
      expect(screen.getByText('John Doe')).toBeInTheDocument();

      // Act - Rerender
      rerender(
        <BrowserRouter>
          <Profile />
        </BrowserRouter>
      );

      // Assert - Should still render correctly
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    test('should handle save operation failure', async () => {
      // Arrange
      const user = userEvent.setup();
      const { toast } = require('sonner');

      // Mock Promise.reject to simulate API failure
      const originalPromise = global.Promise;
      global.Promise = class extends originalPromise {
        static resolve(value) {
          if (typeof value === 'number' && value === 1000) {
            return originalPromise.reject(new Error('API Error'));
          }
          return originalPromise.resolve(value);
        }
      };

      renderWithRouter(<Profile />);

      // Act - Enter edit mode and try to save
      const editButton = screen.getByText('Edit Profile');
      await user.click(editButton);

      const saveButton = screen.getByText('Save');
      await user.click(saveButton);

      // Assert - Should handle error gracefully
      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('Failed to update profile');
      });

      // Restore original Promise
      global.Promise = originalPromise;
    });

    test('should disable buttons during loading state', async () => {
      // Arrange
      const user = userEvent.setup();
      renderWithRouter(<Profile />);

      // Act - Enter edit mode
      const editButton = screen.getByText('Edit Profile');
      await user.click(editButton);

      // Act - Start save operation
      const saveButton = screen.getByText('Save');
      await user.click(saveButton);

      // Assert - Buttons should be disabled during loading
      expect(screen.getByText('Saving...')).toBeDisabled();
      expect(screen.getByText('Cancel')).toBeDisabled();
    });

    test('should handle all form field updates', async () => {
      // Arrange
      const user = userEvent.setup();
      renderWithRouter(<Profile />);

      // Act - Enter edit mode
      const editButton = screen.getByText('Edit Profile');
      await user.click(editButton);

      // Act - Update all editable fields
      const nameField = screen.getByDisplayValue('John Doe');
      const emailField = screen.getByDisplayValue('<EMAIL>');
      const phoneField = screen.getByDisplayValue('+****************');

      await user.clear(nameField);
      await user.type(nameField, 'Jane Smith');

      await user.clear(emailField);
      await user.type(emailField, '<EMAIL>');

      await user.clear(phoneField);
      await user.type(phoneField, '+****************');

      // Assert - All fields should be updated
      expect(screen.getByDisplayValue('Jane Smith')).toBeInTheDocument();
      expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByDisplayValue('+****************')).toBeInTheDocument();
    });

    test('should handle avatar initials for different name formats', () => {
      // This test verifies the avatar initials logic works correctly
      // The component uses userData.name.split(' ').map(n => n[0]).join('')

      // Act
      renderWithRouter(<Profile />);

      // Assert - Should show correct initials for "John Doe"
      const avatar = screen.getByTestId('avatar');
      expect(avatar).toHaveTextContent('JD');
    });
  });
});

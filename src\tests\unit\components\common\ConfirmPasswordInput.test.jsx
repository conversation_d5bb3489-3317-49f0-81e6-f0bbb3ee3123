import React from 'react';
import { render } from '@testing-library/react';
import ConfirmPasswordInput from  '@/components/common/ConfirmPasswordInput';

let mockBasePasswordInput;
jest.mock('@/tests/unit/components/BasePasswordInput', () => {
  mockBasePasswordInput = jest.fn(() => <div data-testid="base-password-input" />);
  return mockBasePasswordInput;
});
import BasePasswordInput from '@/tests/unit/components/BasePasswordInput';

describe('ConfirmPasswordInput', () => {
  const requiredProps = {
    value: '',
    onChange: jest.fn(),
  };

  beforeEach(() => {
    mockBasePasswordInput.mockClear();
  });

  it('renders BasePasswordInput with default label and autoComplete', () => {
    render(<ConfirmPasswordInput {...requiredProps} />);
    expect(mockBasePasswordInput).toHaveBeenCalledWith(
      expect.objectContaining({
        label: 'Confirm Password',
        autoComplete: 'new-password',
      }),
      {}
    );
  });

  it('forwards all props to BasePasswordInput', () => {
    const props = {
      ...requiredProps,
      error: true,
      helperText: 'Error!',
      disabled: true,
      showStartAdornment: false,
      placeholder: 'Confirm',
      size: 'medium',
      sx: { color: 'red' },
    };
    render(<ConfirmPasswordInput {...props} />);
    expect(mockBasePasswordInput).toHaveBeenCalledWith(
      expect.objectContaining(props),
      {}
    );
  });

  it('overrides default label and autoComplete if provided', () => {
    render(<ConfirmPasswordInput {...requiredProps} label="Custom Label" autoComplete="custom" />);
    expect(mockBasePasswordInput).toHaveBeenCalledWith(
      expect.objectContaining({ label: 'Custom Label', autoComplete: 'custom' }),
      {}
    );
  });
}); 
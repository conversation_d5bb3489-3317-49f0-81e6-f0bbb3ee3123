import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import axios from 'axios';
import ForgotPasswordForm from '@/components/ForgotPasswordForm';

// Mock dependencies
jest.mock('axios');
const mockedAxios = axios;

// Mock MUI components
jest.mock('@mui/material', () => {
  const actual = jest.requireActual('@mui/material');
  return {
    ...actual,
    TextField: ({ label, value, onChange, type, disabled, error, helperText }) => (
      <div>
        <label htmlFor="input-email-address">{label}</label>
        <input
          id="input-email-address"
          value={value}
          onChange={onChange}
          type={type}
          disabled={disabled}
        />
        {error && helperText && <span>{helperText}</span>}
      </div>
    ),
  };
});

// Mock the env config
jest.mock('@/config/env', () => ({
  getApiBaseUrl: () => 'http://localhost:8080'
}));

describe('ForgotPasswordForm', () => {
  const mockOnSwitchToLogin = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    process.env.VITE_APP_API_URL = 'http://localhost:8080';
  });

  it('renders forgot password form with all required elements', () => {
    render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);
    expect(screen.getByText('Forgot Password')).toBeInTheDocument();
    expect(screen.getByText("Enter your email address and we'll send you a link to reset your password")).toBeInTheDocument();
    expect(screen.getByLabelText('Email Address')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /send reset link/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /back to sign in/i })).toBeInTheDocument();
  });

  it('updates email when user types', async () => {
    const user = userEvent.setup();
    render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);
    const emailInput = screen.getByLabelText('Email Address');
    await user.type(emailInput, '<EMAIL>');
    expect(emailInput).toHaveValue('<EMAIL>');
  });

  it('shows validation error for empty email', async () => {
    const user = userEvent.setup();
    render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);
    const submitButton = screen.getByRole('button', { name: /send reset link/i });
    await user.click(submitButton);
    expect(await screen.findByText('Email is required')).toBeInTheDocument();
  });

  it('shows validation error for invalid email', async () => {
    const user = userEvent.setup();
    render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);
    const emailInput = screen.getByLabelText('Email Address');
    await user.type(emailInput, 'invalid-email');
    const submitButton = screen.getByRole('button', { name: /send reset link/i });
    await user.click(submitButton);
    expect(await screen.findByText('Please enter a valid email address')).toBeInTheDocument();
  });

  it('clears error when user starts typing', async () => {
    const user = userEvent.setup();
    render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);
    const submitButton = screen.getByRole('button', { name: /send reset link/i });
    await user.click(submitButton);
    expect(await screen.findByText('Email is required')).toBeInTheDocument();
    const emailInput = screen.getByLabelText('Email Address');
    await user.type(emailInput, 'a');
    expect(screen.queryByText('Email is required')).not.toBeInTheDocument();
  });

  it('handles successful password reset request', async () => {
    const user = userEvent.setup();
    mockedAxios.get.mockResolvedValue({ data: { ip: '127.0.0.1' } });
    mockedAxios.post.mockResolvedValue({ status: 200, data: { success: true } });
    render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);
    const emailInput = screen.getByLabelText('Email Address');
    await user.type(emailInput, '<EMAIL>');
    const submitButton = screen.getByRole('button', { name: /send reset link/i });
    await user.click(submitButton);
    expect(await screen.findByText('Check Your Email')).toBeInTheDocument();
    expect(screen.getByText('Password reset request submitted')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /back to sign in/i })).toBeInTheDocument();
  });

  it('handles API errors gracefully and still shows success message', async () => {
    const user = userEvent.setup();
    mockedAxios.get.mockRejectedValue(new Error('Network error'));
    mockedAxios.post.mockRejectedValue(new Error('Network error'));
    render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);
    const emailInput = screen.getByLabelText('Email Address');
    await user.type(emailInput, '<EMAIL>');
    const submitButton = screen.getByRole('button', { name: /send reset link/i });
    await user.click(submitButton);
    expect(await screen.findByText('Check Your Email')).toBeInTheDocument();
    expect(screen.getByText('Password reset request submitted')).toBeInTheDocument();
  });

  it('allows user to try again after email sent', async () => {
    const user = userEvent.setup();
    mockedAxios.get.mockResolvedValue({ data: { ip: '127.0.0.1' } });
    mockedAxios.post.mockResolvedValue({ status: 200, data: { success: true } });
    render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);
    const emailInput = screen.getByLabelText('Email Address');
    await user.type(emailInput, '<EMAIL>');
    const submitButton = screen.getByRole('button', { name: /send reset link/i });
    await user.click(submitButton);
    expect(await screen.findByText('Check Your Email')).toBeInTheDocument();
    const tryAgainButton = screen.getByRole('button', { name: /try again/i });
    await user.click(tryAgainButton);
    expect(screen.getByText('Forgot Password')).toBeInTheDocument();
  });

  it('calls onSwitchToLogin when back to sign in is clicked', async () => {
    const user = userEvent.setup();
    render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);
    const backToLoginButton = screen.getByRole('button', { name: /back to sign in/i });
    await user.click(backToLoginButton);
    expect(mockOnSwitchToLogin).toHaveBeenCalledTimes(1);
  });

  it('calls onSwitchToLogin from email sent view', async () => {
    const user = userEvent.setup();
    mockedAxios.get.mockResolvedValue({ data: { ip: '127.0.0.1' } });
    mockedAxios.post.mockResolvedValue({ status: 200, data: { success: true } });
    render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);
    const emailInput = screen.getByLabelText('Email Address');
    await user.type(emailInput, '<EMAIL>');
    const submitButton = screen.getByRole('button', { name: /send reset link/i });
    await user.click(submitButton);
    expect(await screen.findByText('Check Your Email')).toBeInTheDocument();
    const backToLoginButton = screen.getByRole('button', { name: /back to sign in/i });
    await user.click(backToLoginButton);
    expect(mockOnSwitchToLogin).toHaveBeenCalledTimes(1);
  });
});

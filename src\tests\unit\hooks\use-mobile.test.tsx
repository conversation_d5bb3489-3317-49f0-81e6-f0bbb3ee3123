import { renderHook, act } from '@testing-library/react';
import { useIsMobile } from '@/tests/unit/use-mobile';

// Mock window.matchMedia
const mockMatchMedia = jest.fn();
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: mockMatchMedia,
});

// Mock window.innerWidth
Object.defineProperty(window, 'innerWidth', {
  writable: true,
  value: 1024,
});

describe('useIsMobile', () => {
  let mockMediaQueryList: {
    matches: boolean;
    addEventListener: jest.Mock;
    removeEventListener: jest.Mock;
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Create a mock MediaQueryList
    mockMediaQueryList = {
      matches: false,
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    };
    
    mockMatchMedia.mockReturnValue(mockMediaQueryList);
  });

  describe('Initial State', () => {
    test('should return false for desktop width initially', () => {
      // Arrange
      window.innerWidth = 1024;
      
      // Act
      const { result } = renderHook(() => useIsMobile());
      
      // Assert
      expect(result.current).toBe(false);
    });

    test('should return true for mobile width initially', () => {
      // Arrange
      window.innerWidth = 375;
      
      // Act
      const { result } = renderHook(() => useIsMobile());
      
      // Assert
      expect(result.current).toBe(true);
    });

    test('should return true for width exactly at mobile breakpoint', () => {
      // Arrange
      window.innerWidth = 767; // MOBILE_BREAKPOINT - 1
      
      // Act
      const { result } = renderHook(() => useIsMobile());
      
      // Assert
      expect(result.current).toBe(true);
    });

    test('should return false for width exactly at desktop breakpoint', () => {
      // Arrange
      window.innerWidth = 768; // MOBILE_BREAKPOINT
      
      // Act
      const { result } = renderHook(() => useIsMobile());
      
      // Assert
      expect(result.current).toBe(false);
    });
  });

  describe('Media Query Setup', () => {
    test('should create media query with correct breakpoint', () => {
      // Act
      renderHook(() => useIsMobile());
      
      // Assert
      expect(mockMatchMedia).toHaveBeenCalledWith('(max-width: 767px)');
    });

    test('should add event listener for media query changes', () => {
      // Act
      renderHook(() => useIsMobile());
      
      // Assert
      expect(mockMediaQueryList.addEventListener).toHaveBeenCalledWith(
        'change',
        expect.any(Function)
      );
    });

    test('should remove event listener on unmount', () => {
      // Act
      const { unmount } = renderHook(() => useIsMobile());
      unmount();
      
      // Assert
      expect(mockMediaQueryList.removeEventListener).toHaveBeenCalledWith(
        'change',
        expect.any(Function)
      );
    });
  });

  describe('Window Resize Handling', () => {
    test('should update when window width changes from desktop to mobile', () => {
      // Arrange
      window.innerWidth = 1024;
      const { result } = renderHook(() => useIsMobile());
      
      // Initial state should be false (desktop)
      expect(result.current).toBe(false);
      
      // Act - simulate window resize to mobile
      act(() => {
        window.innerWidth = 375;
        // Get the change handler that was registered
        const changeHandler = mockMediaQueryList.addEventListener.mock.calls[0][1];
        changeHandler();
      });
      
      // Assert
      expect(result.current).toBe(true);
    });

    test('should update when window width changes from mobile to desktop', () => {
      // Arrange
      window.innerWidth = 375;
      const { result } = renderHook(() => useIsMobile());
      
      // Initial state should be true (mobile)
      expect(result.current).toBe(true);
      
      // Act - simulate window resize to desktop
      act(() => {
        window.innerWidth = 1024;
        // Get the change handler that was registered
        const changeHandler = mockMediaQueryList.addEventListener.mock.calls[0][1];
        changeHandler();
      });
      
      // Assert
      expect(result.current).toBe(false);
    });

    test('should handle multiple resize events correctly', () => {
      // Arrange
      window.innerWidth = 1024;
      const { result } = renderHook(() => useIsMobile());
      const changeHandler = mockMediaQueryList.addEventListener.mock.calls[0][1];
      
      // Act & Assert - Multiple resize events
      act(() => {
        window.innerWidth = 375;
        changeHandler();
      });
      expect(result.current).toBe(true);
      
      act(() => {
        window.innerWidth = 1200;
        changeHandler();
      });
      expect(result.current).toBe(false);
      
      act(() => {
        window.innerWidth = 600;
        changeHandler();
      });
      expect(result.current).toBe(true);
    });
  });

  describe('Edge Cases', () => {
    test('should handle window width of 0', () => {
      // Arrange
      window.innerWidth = 0;
      
      // Act
      const { result } = renderHook(() => useIsMobile());
      
      // Assert
      expect(result.current).toBe(true);
    });

    test('should handle very large window width', () => {
      // Arrange
      window.innerWidth = 9999;
      
      // Act
      const { result } = renderHook(() => useIsMobile());
      
      // Assert
      expect(result.current).toBe(false);
    });

    test('should handle negative window width', () => {
      // Arrange
      window.innerWidth = -100;
      
      // Act
      const { result } = renderHook(() => useIsMobile());
      
      // Assert
      expect(result.current).toBe(true);
    });

    test('should handle fractional window width', () => {
      // Arrange
      window.innerWidth = 767.5;

      // Act
      const { result } = renderHook(() => useIsMobile());

      // Assert
      expect(result.current).toBe(true); // 767.5 < 768, so it's mobile
    });
  });

  describe('Hook Lifecycle', () => {
    test('should not cause memory leaks with multiple mounts/unmounts', () => {
      // Act
      const { unmount: unmount1 } = renderHook(() => useIsMobile());
      const { unmount: unmount2 } = renderHook(() => useIsMobile());
      const { unmount: unmount3 } = renderHook(() => useIsMobile());
      
      unmount1();
      unmount2();
      unmount3();
      
      // Assert - Each mount should have added and removed event listeners
      expect(mockMediaQueryList.addEventListener).toHaveBeenCalledTimes(3);
      expect(mockMediaQueryList.removeEventListener).toHaveBeenCalledTimes(3);
    });

    test('should maintain independent state across multiple hook instances', () => {
      // Arrange
      window.innerWidth = 1024;
      
      // Act
      const { result: result1 } = renderHook(() => useIsMobile());
      const { result: result2 } = renderHook(() => useIsMobile());
      
      // Assert - Both instances should return the same value
      expect(result1.current).toBe(false);
      expect(result2.current).toBe(false);
      
      // Both should have set up their own event listeners
      expect(mockMediaQueryList.addEventListener).toHaveBeenCalledTimes(2);
    });
  });

  describe('Boolean Conversion', () => {
    test('should always return a boolean value', () => {
      // Arrange
      window.innerWidth = 1024;
      
      // Act
      const { result } = renderHook(() => useIsMobile());
      
      // Assert
      expect(typeof result.current).toBe('boolean');
    });

    test('should handle undefined initial state correctly', () => {
      // The hook starts with undefined state and converts it to boolean
      // This test ensures the !!isMobile conversion works correctly
      
      // Arrange
      window.innerWidth = 1024;
      
      // Act
      const { result } = renderHook(() => useIsMobile());
      
      // Assert
      expect(result.current).toBe(false);
      expect(typeof result.current).toBe('boolean');
    });
  });

  describe('Breakpoint Constants', () => {
    test('should use 768px as the mobile breakpoint', () => {
      // This test verifies the MOBILE_BREAKPOINT constant is used correctly
      
      // Act
      renderHook(() => useIsMobile());
      
      // Assert
      expect(mockMatchMedia).toHaveBeenCalledWith('(max-width: 767px)');
    });
  });
});

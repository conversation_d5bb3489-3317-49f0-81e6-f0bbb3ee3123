name: CI/CD Pipeline
on:
  push:
    branches:
      - main
      - arc*

jobs:
  # Job 1: Build React Application
  build:
    runs-on: [self-hosted, Linux]
    environment: ${{ github.ref_name == 'main' && 'prod' || github.ref_name == 'dev' && 'dev' || 'local' }}
    outputs:
      build-success: ${{ steps.build-step.outcome == 'success' }}
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: 🔍 Verify Node.js and npm
        run: |
          node --version
          npm --version
          which node
          which npm

      - name: 🧹 Clean npm cache
        run: npm cache clean --force

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🔍 Verify Vite installation
        run: |
          npx vite --version
          ls -la node_modules/.bin/ | grep vite || echo "Vite not found in node_modules/.bin/"

      # - name: 🔍 Lint code
      #   run: npm run lint

      - name: 🏗️ Build application
        id: build-step
        run: |
          echo "Attempting to build with npm run build:spring..."
          npm run build:spring || {
            echo "npm run failed, trying with npx..."
            npx vite build --mode spring
          }

      - name: 📤 Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: dist/
          retention-days: 1


  # Job 2: Docker image Build and Push
   # Job 2: Docker image Build and Push
  docker:
    runs-on: [self-hosted, Linux]
    needs: [build]
    if: github.event_name == 'push' && (github.ref_name == 'main' || startsWith(github.ref_name, 'arc'))
    environment: ${{ github.ref_name == 'main' && 'prod' || github.ref_name == 'dev' && 'dev' || 'local' }}
    outputs:
      image-tag: ${{ steps.docker-build.outputs.image-tag }}
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📥 Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts
          path: dist/

      - name: 🔧 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🏷️ Generate image tags
        id: meta
        run: |
          if [ "${{ github.ref_name }}" = "main" ]; then
            IMAGE_TAG="latest"
          else
            IMAGE_TAG="${{ github.ref_name }}"
          fi
          echo "image-tag=$IMAGE_TAG" >> $GITHUB_OUTPUT
          echo "Generated image tag: $IMAGE_TAG"

      # ✅ Moved this step UP — before `docker build`
      - name: 🔐 Login to Docker Hub
        env:
          DOCKERHUB_TOKEN: ${{ secrets.DOCKERHUB_TOKEN }}
          DOCKERHUB_USERNAME: ${{ secrets.DOCKERHUB_USERNAME }}
        run: |
          echo $DOCKERHUB_TOKEN | docker login -u $DOCKERHUB_USERNAME --password-stdin

      - name: 🐳 Build and Push Docker Image
        id: docker-build
        env:
          DOCKERHUB_TOKEN: ${{ secrets.DOCKERHUB_TOKEN }}
          DOCKERHUB_USERNAME: ${{ secrets.DOCKERHUB_USERNAME }}
        run: |
          IMAGE_TAG="${{ steps.meta.outputs.image-tag }}"

          echo "Building React app Docker image with tags: ${{ github.sha }} and $IMAGE_TAG"

          # Build the Docker image
          docker build \
            -t $DOCKERHUB_USERNAME/ai-react-frontend:${{ github.sha }} \
            -t $DOCKERHUB_USERNAME/ai-react-frontend:$IMAGE_TAG \
            .

          # Push images
          docker push $DOCKERHUB_USERNAME/ai-react-frontend:${{ github.sha }}
          docker push $DOCKERHUB_USERNAME/ai-react-frontend:$IMAGE_TAG

          # Output the image tag for GitOps deployment
          echo "image-tag=$IMAGE_TAG" >> $GITHUB_OUTPUT
          echo "✅ Successfully built and pushed: $DOCKERHUB_USERNAME/ai-react-frontend:$IMAGE_TAG"

  # Job 3: GitOps Deployment Trigger
  deploy-gitops:
    runs-on: [self-hosted, Linux]
    needs: [docker]
    if: github.event_name == 'push' && (github.ref_name == 'main' || startsWith(github.ref_name, 'arc'))
    environment: ${{ github.ref_name == 'main' && 'prod' || github.ref_name == 'dev' && 'dev' || 'local' }}
    steps:
      - name: 🚀 Trigger GitOps Deployment
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITOPS_TOKEN }}  # Make sure this secret exists
          script: |
            console.log('🚀 Triggering GitOps deployment for React application...');
            console.log('Branch:', '${{ github.ref_name }}');
            console.log('Event:', '${{ github.event_name }}');
            console.log('Docker image tag:', '${{ needs.docker.outputs.image-tag }}');

            // Determine environment based on branch - FIXED LOGIC
            let environment = 'dev';  // Default for feature branches
            if ('${{ github.ref_name }}' === 'main') {
              environment = 'dev';  // Changed from 'dev' to 'staging' for main branch
              // Use 'production' if main should go directly to production
            } else if ('${{ github.ref_name }}' === 'arc-runners') {
              environment = 'dev';  // Testing environment
            }
            // Feature branches default to 'dev'

            // Validate required fields before sending
            const dockerTag = '${{ needs.docker.outputs.image-tag }}';
            if (!dockerTag || dockerTag === '') {
              throw new Error('Docker image tag is required but not provided');
            }

            const payload = {
              app_name: 'AI React Frontend',
              project_id: 'ai-react-frontend',
              application_type: 'react-frontend',
              environment: environment, 
              docker_image: 'saipriya104/ai-react-frontend',
              container_port: 80,
              docker_tag: dockerTag,
              source_repo: `${context.repo.owner}/${context.repo.repo}`,
              source_branch: '${{ github.ref_name }}',
              commit_sha: context.sha
            };

            console.log('📦 Dispatch payload springboot-backend:', JSON.stringify(payload, null, 2));

            // Validate payload before sending
            const requiredFields = ['app_name', 'project_id', 'environment', 'docker_image', 'docker_tag'];
            for (const field of requiredFields) {
              if (!payload[field] || payload[field] === '') {
                throw new Error(`Required field '${field}' is missing or empty`);
              }
            }

            // Validate project_id format (must be lowercase alphanumeric with hyphens)
            if (!/^[a-z0-9-]+$/.test(payload.project_id)) {
              throw new Error(`Invalid project_id format: ${payload.project_id}. Must be lowercase alphanumeric with hyphens only.`);
            }

            // Validate environment
            if (!['dev', 'staging', 'production'].includes(payload.environment)) {
              throw new Error(`Invalid environment: ${payload.environment}. Must be dev, staging, or production.`);
            }

            try {
              await github.rest.repos.createDispatchEvent({
                owner: 'ChidhagniConsulting',
                repo: 'gitops-argocd-apps',
                event_type: 'deploy-to-argocd',
                client_payload: payload
              });

              console.log(`✅ GitOps deployment triggered successfully!`);
              console.log(`📱 App: AI React Frontend (ai-react-frontend)`);
              console.log(`🌍 Environment: ${environment}`);
              console.log(`🐳 Docker image: saipriya104/ai-react-frontend:${dockerTag}`);
              console.log(`🌿 Source branch: ${{ github.ref_name }}`);
              console.log(`📝 Commit SHA: ${context.sha}`);
              console.log(`🔗 Monitor deployment: https://github.com/ChidhagniConsulting/gitops-argocd-apps/actions`);
            } catch (error) {
              console.error('❌ Failed to trigger GitOps deployment:', error);
              console.error('Error details:', error.message);
              if (error.response) {
                console.error('Response status:', error.response.status);
                console.error('Response data:', error.response.data);
              }
              throw error;
            }

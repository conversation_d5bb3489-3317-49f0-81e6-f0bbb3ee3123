import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import axios from 'axios';
import ForgotPasswordForm from '@/components/ForgotPasswordForm';

// Mock dependencies
jest.mock('axios');
const mockedAxios = axios;

// Mock the env config
jest.mock('@/tests/config/env', () => ({
  getApiBaseUrl: () => 'http://localhost:8080'
}));

// Mock MUI components
jest.mock('@mui/material', () => ({
  Box: ({ children, component, onSubmit, ...props }) => {
    const Component = component || 'div';
    return <Component onSubmit={onSubmit} {...props}>{children}</Component>;
  },
  TextField: ({ label, value, onChange, error, helperText, type, InputProps, ...props }) => (
    <div>
      <label>{label}</label>
      <input
        type={type || 'text'}
        value={value}
        onChange={(e) => onChange && onChange(e)}
        data-testid={`input-${label?.toLowerCase().replace(/\s+/g, '-')}`}
        {...props}
      />
      {error && <span data-testid={`error-${label?.toLowerCase().replace(/\s+/g, '-')}`}>{helperText}</span>}
      {InputProps?.endAdornment}
    </div>
  ),
  Button: ({ children, onClick, disabled, type, variant, ...props }) => (
    <button type={type} onClick={onClick} disabled={disabled} data-variant={variant} {...props}>
      {children}
    </button>
  ),
  Typography: ({ children, ...props }) => <div {...props}>{children}</div>,
  Link: ({ children, onClick, ...props }) => (
    <button onClick={onClick} {...props}>{children}</button>
  ),
  InputAdornment: ({ children }) => <span>{children}</span>,
  CircularProgress: () => <div data-testid="loading-spinner">Loading...</div>,
  Alert: ({ children, severity, ...props }) => (
    <div data-testid={`alert-${severity}`} {...props}>{children}</div>
  ),
}));

// Mock Lucide React icons
jest.mock('lucide-react', () => ({
  Mail: () => <span>Mail Icon</span>,
}));

describe('ForgotPasswordForm Real Implementation', () => {
  const mockOnSwitchToLogin = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock IP address API
    mockedAxios.get.mockResolvedValue({
      data: { ip: '***********' }
    });
  });

  it('renders forgot password form with all required elements', () => {
    render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);

    expect(screen.getByText('Forgot Password')).toBeInTheDocument();
    expect(screen.getByText('Enter your email address and we\'ll send you a link to reset your password')).toBeInTheDocument();
    expect(screen.getByTestId('input-email-address')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /send reset link/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
  });

  it('updates email when user types', async () => {
    const user = userEvent.setup();
    render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);

    const emailInput = screen.getByTestId('input-email-address');
    await user.type(emailInput, '<EMAIL>');

    expect(emailInput.value).toBe('<EMAIL>');
  });

  it('shows validation error for empty email', async () => {
    const user = userEvent.setup();
    render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);

    const submitButton = screen.getByRole('button', { name: /send reset link/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByTestId('error-email-address')).toHaveTextContent('Email is required');
    });
  });

  it('shows validation error for invalid email format', async () => {
    const user = userEvent.setup();
    render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);

    const emailInput = screen.getByTestId('input-email-address');
    const submitButton = screen.getByRole('button', { name: /send reset link/i });

    await user.type(emailInput, 'invalid-email');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByTestId('error-email-address')).toHaveTextContent('Please enter a valid email address');
    });
  });

  it('clears error when user starts typing', async () => {
    const user = userEvent.setup();
    render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);

    // First trigger validation error
    const submitButton = screen.getByRole('button', { name: /send reset link/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByTestId('error-email-address')).toBeInTheDocument();
    });

    // Then start typing to clear the error
    const emailInput = screen.getByTestId('input-email-address');
    await user.type(emailInput, '<EMAIL>');

    await waitFor(() => {
      expect(screen.queryByTestId('error-email-address')).not.toBeInTheDocument();
    });
  });

  it('handles successful password reset request', async () => {
    const user = userEvent.setup();
    
    // Mock forgot password API
    mockedAxios.post.mockResolvedValue({
      status: 200,
      data: { success: true }
    });

    render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);

    const emailInput = screen.getByTestId('input-email-address');
    const submitButton = screen.getByRole('button', { name: /send reset link/i });

    await user.type(emailInput, '<EMAIL>');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByTestId('alert-info')).toBeInTheDocument();
      expect(screen.getByText('Check Your Email')).toBeInTheDocument();
    });

    expect(mockedAxios.post).toHaveBeenCalledWith(
      'http://localhost:8080/api/v1/forgot-password',
      {
        email: '<EMAIL>',
        ipAddress: '***********',
        deviceDetails: navigator.userAgent,
      },
      {
        headers: { 'Content-Type': 'application/json' },
        validateStatus: expect.any(Function),
      }
    );
  });

  it('handles API errors gracefully and still shows success message', async () => {
    const user = userEvent.setup();
    
    // Mock IP address API failure
    mockedAxios.get.mockRejectedValue(new Error('IP fetch failed'));
    
    // Mock forgot password API failure
    mockedAxios.post.mockRejectedValue(new Error('Network error'));

    render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);

    const emailInput = screen.getByTestId('input-email-address');
    const submitButton = screen.getByRole('button', { name: /send reset link/i });

    await user.type(emailInput, '<EMAIL>');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByTestId('alert-info')).toBeInTheDocument();
      expect(screen.getByText('Check Your Email')).toBeInTheDocument();
    });
  });

  it('allows user to try again after email sent', async () => {
    const user = userEvent.setup();
    
    mockedAxios.post.mockResolvedValue({ status: 200, data: { success: true } });

    render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);

    // Submit form first
    const emailInput = screen.getByTestId('input-email-address');
    await user.type(emailInput, '<EMAIL>');
    await user.click(screen.getByRole('button', { name: /send reset link/i }));

    await waitFor(() => {
      expect(screen.getByText('Check Your Email')).toBeInTheDocument();
    });

    // Click try again
    const tryAgainButton = screen.getByRole('button', { name: /try again/i });
    await user.click(tryAgainButton);

    // Should be back to the form
    expect(screen.getByText('Forgot Password')).toBeInTheDocument();
    expect(screen.getByTestId('input-email-address')).toBeInTheDocument();
  });

  it('calls onSwitchToLogin when back to login is clicked', async () => {
    const user = userEvent.setup();
    render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);

    const backToLoginButton = screen.getByRole('button', { name: /sign in/i });
    await user.click(backToLoginButton);

    expect(mockOnSwitchToLogin).toHaveBeenCalledTimes(1);
  });

  it('calls onSwitchToLogin from email sent view', async () => {
    const user = userEvent.setup();
    
    mockedAxios.post.mockResolvedValue({ status: 200, data: { success: true } });

    render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);

    // Submit form to get to email sent view
    const emailInput = screen.getByTestId('input-email-address');
    await user.type(emailInput, '<EMAIL>');
    await user.click(screen.getByRole('button', { name: /send reset link/i }));

    await waitFor(() => {
      expect(screen.getByText('Check Your Email')).toBeInTheDocument();
    });

    // Click back to login from email sent view
    const backToLoginButtons = screen.getAllByRole('button', { name: /sign in/i });
    await user.click(backToLoginButtons[0]); // First occurrence in email sent view

    expect(mockOnSwitchToLogin).toHaveBeenCalledTimes(1);
  });

  it('shows loading state when submitting', async () => {
    const user = userEvent.setup();
    
    // Mock a delayed response
    mockedAxios.post.mockImplementation(() => new Promise(resolve => setTimeout(() => resolve({ status: 200, data: { success: true } }), 100)));

    render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);

    const emailInput = screen.getByTestId('input-email-address');
    const submitButton = screen.getByRole('button', { name: /send reset link/i });

    await user.type(emailInput, '<EMAIL>');
    await user.click(submitButton);

    // Should show loading state
    expect(screen.getByRole('button', { name: /loading/i })).toBeDisabled();
  });

  it('handles IP address fetch failure gracefully', async () => {
    const user = userEvent.setup();

    // Mock IP address API failure but forgot password API success
    mockedAxios.get.mockRejectedValue(new Error('IP fetch failed'));
    mockedAxios.post.mockResolvedValue({ status: 200, data: { success: true } });

    render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);

    const emailInput = screen.getByTestId('input-email-address');
    const submitButton = screen.getByRole('button', { name: /send reset link/i });

    await user.type(emailInput, '<EMAIL>');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Check Your Email')).toBeInTheDocument();
    });

    // Should still call the API with empty IP address
    expect(mockedAxios.post).toHaveBeenCalledWith(
      'http://localhost:8080/api/v1/forgot-password',
      {
        email: '<EMAIL>',
        ipAddress: '',
        deviceDetails: navigator.userAgent,
      },
      expect.any(Object)
    );
  });

  it('uses validateStatus function in axios config', async () => {
    const user = userEvent.setup();
    mockedAxios.post.mockResolvedValue({
      status: 400,
      data: { error: 'Bad request' }
    });

    render(<ForgotPasswordForm onSwitchToLogin={mockOnSwitchToLogin} />);

    const emailInput = screen.getByTestId('input-email-address');
    const submitButton = screen.getByRole('button', { name: /send reset link/i });

    await user.type(emailInput, '<EMAIL>');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Check Your Email')).toBeInTheDocument();
    });

    // Verify that validateStatus function was called and returns true for any status
    expect(mockedAxios.post).toHaveBeenCalledWith(
      'http://localhost:8080/api/v1/forgot-password',
      {
        email: '<EMAIL>',
        ipAddress: '***********',
        deviceDetails: navigator.userAgent,
      },
      {
        headers: { 'Content-Type': 'application/json' },
        validateStatus: expect.any(Function),
      }
    );

    // Test that the validateStatus function returns true for any status code
    const axiosConfig = mockedAxios.post.mock.calls[mockedAxios.post.mock.calls.length - 1][2];
    expect(axiosConfig.validateStatus()).toBe(true);
    expect(axiosConfig.validateStatus(200)).toBe(true);
    expect(axiosConfig.validateStatus(400)).toBe(true);
    expect(axiosConfig.validateStatus(500)).toBe(true);
  });
});

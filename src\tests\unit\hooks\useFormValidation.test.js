import { renderHook, act } from '@testing-library/react';
import { waitFor } from '@testing-library/react';
import useFormValidation from '@/hooks/useFormValidation';
import { EMAIL_MESSAGES, VALIDATION_MESSAGES, REGEX_PATTERNS } from '@/constants/validationMessages';

describe('useFormValidation', () => {
  const initialValues = {
    email: '',
    name: '',
    mobile: '',
    agreeToTerms: false,
  };

  it('should initialize with initial values', () => {
    const { result } = renderHook(() => useFormValidation(initialValues));
    expect(result.current.values).toEqual(initialValues);
    expect(result.current.errors).toEqual({});
    expect(result.current.touched).toEqual({});
    expect(result.current.isValid).toBe(true);
  });

  it('should validate email field (required)', async () => {
    const { result } = renderHook(() => useFormValidation(initialValues));
    await act(async () => {
      result.current.handleInputChange('email', '');
      result.current.handleBlur('email');
    });
    expect(result.current.errors.email).toBe(EMAIL_MESSAGES.REQUIRED);
  });

  it('should validate email field (invalid)', async () => {
    const { result } = renderHook(() => useFormValidation(initialValues));
    await act(async () => {
      result.current.handleInputChange('email', '<EMAIL>');
      result.current.handleBlur('email');
    });
    await act(async () => {
      result.current.handleInputChange('email', 'invalid-email');
    });
    expect(result.current.errors.email).toBeUndefined();
    await act(async () => {
      result.current.handleBlur('email');
    });
    expect(result.current.errors.email).toBe(EMAIL_MESSAGES.INVALID);
  });

  it('should validate email field (valid)', async () => {
    const { result } = renderHook(() => useFormValidation(initialValues));
    await act(async () => {
      result.current.handleInputChange('email', 'invalid-email');
      result.current.handleBlur('email');
    });
    await act(async () => {
      result.current.handleInputChange('email', '<EMAIL>');
    });
    expect(result.current.errors.email).toBeUndefined();
  });

  it('should validate name field (required)', async () => {
    const { result } = renderHook(() => useFormValidation(initialValues));
    await act(async () => {
      result.current.handleInputChange('name', '');
      result.current.handleBlur('name');
    });
    expect(result.current.errors.name).toBe(VALIDATION_MESSAGES.NAME_REQUIRED);
  });

  it('should validate name field (min length)', async () => {
    const { result } = renderHook(() => useFormValidation(initialValues));
    await act(async () => {
      result.current.handleInputChange('name', 'John Doe');
      result.current.handleBlur('name');
    });
    await act(async () => {
      result.current.handleInputChange('name', 'A');
    });
    expect(result.current.errors.name).toBeUndefined();
    await act(async () => {
      result.current.handleBlur('name');
    });
    expect(result.current.errors.name).toBe(VALIDATION_MESSAGES.NAME_MIN_LENGTH);
  });

  it('should validate name field (valid)', async () => {
    const { result } = renderHook(() => useFormValidation(initialValues));
    await act(async () => {
      result.current.handleInputChange('name', 'A');
      result.current.handleBlur('name');
    });
    await act(async () => {
      result.current.handleInputChange('name', 'John Doe');
    });
    expect(result.current.errors.name).toBeUndefined();
  });

  it('should validate mobile field (required)', async () => {
    const { result } = renderHook(() => useFormValidation(initialValues));
    await act(async () => {
      result.current.handleInputChange('mobile', '');
      result.current.handleBlur('mobile');
    });
    expect(result.current.errors.mobile).toBe(VALIDATION_MESSAGES.MOBILE_REQUIRED);
  });

  it('should validate mobile field (invalid)', async () => {
    const { result } = renderHook(() => useFormValidation(initialValues));
    await act(async () => {
      result.current.handleInputChange('mobile', '1234567890');
      result.current.handleBlur('mobile');
    });
    await act(async () => {
      result.current.handleInputChange('mobile', '123');
    });
    expect(result.current.errors.mobile).toBeUndefined();
    await act(async () => {
      result.current.handleBlur('mobile');
    });
    expect(result.current.errors.mobile).toBe(VALIDATION_MESSAGES.MOBILE_INVALID);
  });

  it('should validate mobile field (valid)', async () => {
    const { result } = renderHook(() => useFormValidation(initialValues));
    await act(async () => {
      result.current.handleInputChange('mobile', '123');
      result.current.handleBlur('mobile');
    });
    await act(async () => {
      result.current.handleInputChange('mobile', '1234567890');
    });
    expect(result.current.errors.mobile).toBeUndefined();
  });

  it('should validate agreeToTerms field (required)', async () => {
    const { result } = renderHook(() => useFormValidation(initialValues));
    await act(async () => {
      result.current.handleInputChange('agreeToTerms', false);
      result.current.handleBlur('agreeToTerms');
    });
    expect(result.current.errors.agreeToTerms).toBe(VALIDATION_MESSAGES.TERMS_REQUIRED);
  });

  it('should validate agreeToTerms field (valid)', async () => {
    const { result } = renderHook(() => useFormValidation(initialValues));
    await act(async () => {
      result.current.handleInputChange('agreeToTerms', false);
      result.current.handleBlur('agreeToTerms');
    });
    await act(async () => {
      result.current.handleInputChange('agreeToTerms', true);
    });
    expect(result.current.errors.agreeToTerms).toBeUndefined();
  });

  it('should validate the whole form and set errors', async () => {
    const { result } = renderHook(() => useFormValidation(initialValues));
    let valid;
    await act(async () => {
      valid = result.current.validateForm();
    });
    expect(valid).toBe(false);
    expect(result.current.errors.email).toBe(EMAIL_MESSAGES.REQUIRED);
    expect(result.current.errors.name).toBe(VALIDATION_MESSAGES.NAME_REQUIRED);
    expect(result.current.errors.mobile).toBe(VALIDATION_MESSAGES.MOBILE_REQUIRED);
    expect(result.current.errors.agreeToTerms).toBe(VALIDATION_MESSAGES.TERMS_REQUIRED);
  });

  it('should clear error on input change', async () => {
    const { result } = renderHook(() => useFormValidation(initialValues));
    await act(async () => {
      result.current.handleBlur('email');
    });
    expect(result.current.errors.email).toBe(EMAIL_MESSAGES.REQUIRED);
    await act(async () => {
      result.current.handleInputChange('email', '<EMAIL>');
    });
    expect(result.current.errors.email).toBeUndefined();
  });

  it('should set touched state on blur', async () => {
    const { result } = renderHook(() => useFormValidation(initialValues));
    await act(async () => {
      result.current.handleBlur('email');
    });
    expect(result.current.touched.email).toBe(true);
  });

  it('should reset form to initial values', async () => {
    const { result } = renderHook(() => useFormValidation(initialValues));
    await act(async () => {
      result.current.handleInputChange('email', '<EMAIL>');
      result.current.handleInputChange('name', 'John Doe');
      result.current.handleInputChange('mobile', '1234567890');
      result.current.handleInputChange('agreeToTerms', true);
    });
    expect(result.current.values).toEqual({
      email: '<EMAIL>',
      name: 'John Doe',
      mobile: '1234567890',
      agreeToTerms: true,
    });
    await act(async () => {
      result.current.resetForm();
    });
    expect(result.current.values).toEqual(initialValues);
    expect(result.current.errors).toEqual({});
    expect(result.current.touched).toEqual({});
  });

  it('should set field value, error, and touched manually', async () => {
    const { result } = renderHook(() => useFormValidation(initialValues));
    await act(async () => {
      result.current.setFieldValue('email', '<EMAIL>');
      result.current.setFieldError('email', 'Custom error');
      result.current.setFieldTouched('email', true);
    });
    expect(result.current.values.email).toBe('<EMAIL>');
    expect(result.current.errors.email).toBe('Custom error');
    expect(result.current.touched.email).toBe(true);
  });

  it('should return isValid as true only if there are no errors', async () => {
    const { result } = renderHook(() => useFormValidation(initialValues));
    await act(async () => {
      result.current.handleInputChange('email', '<EMAIL>');
      result.current.handleInputChange('name', 'John Doe');
      result.current.handleInputChange('mobile', '1234567890');
      result.current.handleInputChange('agreeToTerms', true);
      result.current.validateForm();
    });
    await waitFor(() => {
      expect(result.current.isValid).toBe(true);
    });
  });
}); 
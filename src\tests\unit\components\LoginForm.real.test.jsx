import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { toast } from 'sonner';
import LoginForm from '@/components/LoginForm';

// Mock dependencies
jest.mock('sonner');
jest.mock('@/components/GoogleLoginButton', () => {
  return function MockGoogleLoginButton() {
    return <div data-testid="google-login-button">Google Login</div>;
  };
});

// Mock AuthContext
const mockLogin = jest.fn();
const mockUseAuth = jest.fn(() => ({
  login: mockLogin,
  isLoading: false,
}));

jest.mock('@/contexts/AuthContext', () => ({
  useAuth: () => mockUseAuth(),
}));

// Mock MUI components
jest.mock('@mui/material', () => ({
  Box: ({ children, component, onSubmit, ...props }) => {
    const Component = component || 'div';
    return <Component onSubmit={onSubmit} {...props}>{children}</Component>;
  },
  TextField: ({ label, value, onChange, error, helperText, type, InputProps, ...props }) => (
    <div>
      <label>{label}</label>
      <input
        type={type || 'text'}
        value={value}
        onChange={(e) => onChange && onChange(e)}
        data-testid={`input-${label?.toLowerCase().replace(/\s+/g, '-')}`}
        {...props}
      />
      {error && <span data-testid={`error-${label?.toLowerCase().replace(/\s+/g, '-')}`}>{helperText}</span>}
      {InputProps?.endAdornment}
    </div>
  ),
  Button: ({ children, onClick, disabled, type, ...props }) => (
    <button type={type} onClick={onClick} disabled={disabled} {...props}>
      {children}
    </button>
  ),
  Typography: ({ children, ...props }) => <div {...props}>{children}</div>,
  Link: ({ children, onClick, ...props }) => (
    <button onClick={onClick} {...props}>{children}</button>
  ),
  InputAdornment: ({ children }) => <span>{children}</span>,
  IconButton: ({ children, onClick }) => <button onClick={onClick}>{children}</button>,
  CircularProgress: () => <div data-testid="loading-spinner">Loading...</div>,
}));

// Mock Lucide React icons
jest.mock('lucide-react', () => ({
  Mail: () => <span>Mail Icon</span>,
  Lock: () => <span>Lock Icon</span>,
  Eye: () => <span>Eye Icon</span>,
  EyeOff: () => <span>EyeOff Icon</span>,
}));

describe('LoginForm Real Implementation', () => {
  const mockOnSwitchToSignUp = jest.fn();
  const mockOnSwitchToForgotPassword = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAuth.mockReturnValue({
      login: mockLogin,
      isLoading: false,
    });
  });

  it('renders login form with all required fields', () => {
    render(
      <LoginForm 
        onSwitchToSignUp={mockOnSwitchToSignUp}
        onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
      />
    );

    expect(screen.getByText('Welcome Back')).toBeInTheDocument();
    expect(screen.getByText('Sign in to your account to continue')).toBeInTheDocument();
    expect(screen.getByTestId('input-email-address')).toBeInTheDocument();
    expect(screen.getByTestId('input-password')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
    expect(screen.getByTestId('google-login-button')).toBeInTheDocument();
  });

  it('updates form data when user types in fields', async () => {
    const user = userEvent.setup();
    render(
      <LoginForm 
        onSwitchToSignUp={mockOnSwitchToSignUp}
        onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
      />
    );

    const emailInput = screen.getByTestId('input-email-address');
    const passwordInput = screen.getByTestId('input-password');

    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');

    expect(emailInput.value).toBe('<EMAIL>');
    expect(passwordInput.value).toBe('password123');
  });

  it('shows validation errors for empty fields', async () => {
    const user = userEvent.setup();
    render(
      <LoginForm 
        onSwitchToSignUp={mockOnSwitchToSignUp}
        onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
      />
    );

    const submitButton = screen.getByRole('button', { name: /sign in/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByTestId('error-email-address')).toHaveTextContent('Email is required');
      expect(screen.getByTestId('error-password')).toHaveTextContent('Password is required');
    });
  });

  it('shows validation error for short password', async () => {
    const user = userEvent.setup();
    render(
      <LoginForm 
        onSwitchToSignUp={mockOnSwitchToSignUp}
        onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
      />
    );

    const passwordInput = screen.getByTestId('input-password');
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    await user.type(passwordInput, '123');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByTestId('error-password')).toHaveTextContent('Password must be at least 6 characters');
    });
  });

  it('calls login function with correct credentials on valid form submission', async () => {
    const user = userEvent.setup();
    render(
      <LoginForm 
        onSwitchToSignUp={mockOnSwitchToSignUp}
        onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
      />
    );

    const emailInput = screen.getByTestId('input-email-address');
    const passwordInput = screen.getByTestId('input-password');
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith('<EMAIL>', 'password123');
    });
  });

  it('shows success toast on successful login', async () => {
    const user = userEvent.setup();
    mockLogin.mockResolvedValue();
    
    render(
      <LoginForm 
        onSwitchToSignUp={mockOnSwitchToSignUp}
        onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
      />
    );

    const emailInput = screen.getByTestId('input-email-address');
    const passwordInput = screen.getByTestId('input-password');
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    await user.click(submitButton);

    await waitFor(() => {
      expect(toast.success).toHaveBeenCalledWith('Successfully logged in!');
    });
  });

  it('shows error toast on failed login', async () => {
    const user = userEvent.setup();
    mockLogin.mockRejectedValue(new Error('Login failed'));
    
    render(
      <LoginForm 
        onSwitchToSignUp={mockOnSwitchToSignUp}
        onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
      />
    );

    const emailInput = screen.getByTestId('input-email-address');
    const passwordInput = screen.getByTestId('input-password');
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    await user.click(submitButton);

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Invalid email or password. Please try again.');
    });
  });

  it('clears field errors when user starts typing', async () => {
    const user = userEvent.setup();
    render(
      <LoginForm 
        onSwitchToSignUp={mockOnSwitchToSignUp}
        onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
      />
    );

    // First trigger validation errors
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByTestId('error-email-address')).toBeInTheDocument();
      expect(screen.getByTestId('error-password')).toBeInTheDocument();
    });

    // Then start typing to clear errors
    const emailInput = screen.getByTestId('input-email-address');
    const passwordInput = screen.getByTestId('input-password');

    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');

    await waitFor(() => {
      expect(screen.queryByTestId('error-email-address')).not.toBeInTheDocument();
      expect(screen.queryByTestId('error-password')).not.toBeInTheDocument();
    });
  });

  it('toggles password visibility', async () => {
    const user = userEvent.setup();
    render(
      <LoginForm 
        onSwitchToSignUp={mockOnSwitchToSignUp}
        onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
      />
    );

    const passwordInput = screen.getByTestId('input-password');
    const toggleButton = screen.getByRole('button', { name: 'Eye Icon' }); // IconButton with Eye Icon

    expect(passwordInput.type).toBe('password');

    await user.click(toggleButton);
    expect(passwordInput.type).toBe('text');

    await user.click(toggleButton);
    expect(passwordInput.type).toBe('password');
  });

  it('calls onSwitchToSignUp when sign up link is clicked', async () => {
    const user = userEvent.setup();
    render(
      <LoginForm 
        onSwitchToSignUp={mockOnSwitchToSignUp}
        onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
      />
    );

    const signUpLink = screen.getByRole('button', { name: /sign up/i });
    await user.click(signUpLink);

    expect(mockOnSwitchToSignUp).toHaveBeenCalledTimes(1);
  });

  it('calls onSwitchToForgotPassword when forgot password link is clicked', async () => {
    const user = userEvent.setup();
    render(
      <LoginForm 
        onSwitchToSignUp={mockOnSwitchToSignUp}
        onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
      />
    );

    const forgotPasswordLink = screen.getByRole('button', { name: /forgot password/i });
    await user.click(forgotPasswordLink);

    expect(mockOnSwitchToForgotPassword).toHaveBeenCalledTimes(1);
  });

  it('shows loading state when isLoading is true', () => {
    mockUseAuth.mockReturnValue({
      login: mockLogin,
      isLoading: true,
    });

    render(
      <LoginForm 
        onSwitchToSignUp={mockOnSwitchToSignUp}
        onSwitchToForgotPassword={mockOnSwitchToForgotPassword}
      />
    );

    const submitButton = screen.getByRole('button', { name: /loading/i });
    expect(submitButton).toBeDisabled();
  });
});

# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production build (will be created during Docker build)
dist
build

# Development files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
coverage
*.test.js
*.test.ts
*.test.jsx
*.test.tsx
__tests__
src/tests
src/__mocks__
jest-results.json

# IDE and editor files
.vscode
.idea
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# CI/CD
.github

# Documentation
README.md
*.md
docs

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# SonarQube
sonar-project.properties
docker-compose.sonarqube.yml

# Test and development scripts
*.bat
*.cmd
*.ps1
run-*.cmd
test-*.cmd

# Kubernetes
k8s

# Scripts
scripts

# Docker files
Dockerfile
docker-compose*
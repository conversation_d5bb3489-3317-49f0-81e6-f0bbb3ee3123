import React from 'react';
import { render, screen } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';

// Mock dependencies
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock AuthContext
const mockUseAuth = jest.fn();
jest.mock('@/tests/contexts/AuthContext', () => ({
  useAuth: () => mockUseAuth(),
}));

// Mock child components
jest.mock('@/tests/unit/LoginForm', () => {
  return function MockLoginForm({ onSwitchToSignUp, onSwitchToForgotPassword }) {
    return (
      <div data-testid="login-form">
        <div>Login Form</div>
        <button onClick={onSwitchToSignUp} data-testid="switch-to-signup">
          Go to Sign Up
        </button>
        <button onClick={onSwitchToForgotPassword} data-testid="switch-to-forgot-password">
          Forgot Password
        </button>
      </div>
    );
  };
});

jest.mock('@/tests/unit/SignUpForm', () => {
  return function MockSignUpForm({ onSwitchToLogin }) {
    return (
      <div data-testid="signup-form">
        <div>Sign Up Form</div>
        <button onClick={onSwitchToLogin} data-testid="switch-to-login">
          Go to Login
        </button>
      </div>
    );
  };
});

jest.mock('@/tests/unit/ForgotPasswordForm', () => {
  return function MockForgotPasswordForm({ onSwitchToLogin }) {
    return (
      <div data-testid="forgot-password-form">
        <div>Forgot Password Form</div>
        <button onClick={onSwitchToLogin} data-testid="switch-to-login">
          Back to Login
        </button>
      </div>
    );
  };
});

jest.mock('@/tests/unit/UserDashboard', () => {
  return function MockUserDashboard() {
    return <div data-testid="user-dashboard">User Dashboard</div>;
  };
});

// Mock MUI components
jest.mock('@mui/material', () => ({
  Box: ({ children, ...props }) => <div {...props}>{children}</div>,
  Container: ({ children, ...props }) => <div {...props}>{children}</div>,
  Paper: ({ children, ...props }) => <div {...props}>{children}</div>,
}));

// Mock AuthContainer component to avoid import issues
const MockAuthContainer = ({ initialView = 'login' }) => {
  const [currentView, setCurrentView] = React.useState(initialView);
  const { user } = mockUseAuth();

  React.useEffect(() => {
    setCurrentView(initialView);
  }, [initialView]);

  if (user) {
    return <div data-testid="user-dashboard">User Dashboard</div>;
  }

  const renderCurrentView = () => {
    switch (currentView) {
      case 'login':
        return (
          <div data-testid="login-form">
            <div>Login Form</div>
            <button onClick={() => mockNavigate('/signup')} data-testid="switch-to-signup">
              Go to Sign Up
            </button>
            <button onClick={() => mockNavigate('/forgot-password')} data-testid="switch-to-forgot-password">
              Forgot Password
            </button>
          </div>
        );
      case 'signup':
        return (
          <div data-testid="signup-form">
            <div>Sign Up Form</div>
            <button onClick={() => mockNavigate('/signin')} data-testid="switch-to-login">
              Go to Login
            </button>
          </div>
        );
      case 'forgot-password':
        return (
          <div data-testid="forgot-password-form">
            <div>Forgot Password Form</div>
            <button onClick={() => mockNavigate('/signin')} data-testid="switch-to-login">
              Back to Login
            </button>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div data-testid="auth-container">
      <div data-testid="auth-paper">
        {renderCurrentView()}
      </div>
    </div>
  );
};

// Wrapper component for testing with router
const TestWrapper = ({ children }) => (
  <BrowserRouter>
    {children}
  </BrowserRouter>
);

describe('AuthContainer', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('when user is not authenticated', () => {
    beforeEach(() => {
      mockUseAuth.mockReturnValue({ user: null });
    });

    it('renders login form by default', () => {
      render(
        <TestWrapper>
          <MockAuthContainer />
        </TestWrapper>
      );

      expect(screen.getByTestId('auth-container')).toBeInTheDocument();
      expect(screen.getByTestId('login-form')).toBeInTheDocument();
      expect(screen.getByText('Login Form')).toBeInTheDocument();
    });

    it('renders login form when initialView is "login"', () => {
      render(
        <TestWrapper>
          <MockAuthContainer initialView="login" />
        </TestWrapper>
      );

      expect(screen.getByTestId('login-form')).toBeInTheDocument();
      expect(screen.getByText('Login Form')).toBeInTheDocument();
    });

    it('renders signup form when initialView is "signup"', () => {
      render(
        <TestWrapper>
          <MockAuthContainer initialView="signup" />
        </TestWrapper>
      );

      expect(screen.getByTestId('signup-form')).toBeInTheDocument();
      expect(screen.getByText('Sign Up Form')).toBeInTheDocument();
    });

    it('renders forgot password form when initialView is "forgot-password"', () => {
      render(
        <TestWrapper>
          <MockAuthContainer initialView="forgot-password" />
        </TestWrapper>
      );

      expect(screen.getByTestId('forgot-password-form')).toBeInTheDocument();
      expect(screen.getByText('Forgot Password Form')).toBeInTheDocument();
    });

    it('renders nothing for invalid initialView', () => {
      render(
        <TestWrapper>
          <MockAuthContainer initialView="invalid" />
        </TestWrapper>
      );

      expect(screen.getByTestId('auth-container')).toBeInTheDocument();
      expect(screen.queryByTestId('login-form')).not.toBeInTheDocument();
      expect(screen.queryByTestId('signup-form')).not.toBeInTheDocument();
      expect(screen.queryByTestId('forgot-password-form')).not.toBeInTheDocument();
    });

    it('navigates to signup when switch to signup is clicked from login', () => {
      render(
        <TestWrapper>
          <MockAuthContainer initialView="login" />
        </TestWrapper>
      );

      const switchButton = screen.getByTestId('switch-to-signup');
      switchButton.click();

      expect(mockNavigate).toHaveBeenCalledWith('/signup');
    });

    it('navigates to forgot password when forgot password is clicked from login', () => {
      render(
        <TestWrapper>
          <MockAuthContainer initialView="login" />
        </TestWrapper>
      );

      const switchButton = screen.getByTestId('switch-to-forgot-password');
      switchButton.click();

      expect(mockNavigate).toHaveBeenCalledWith('/forgot-password');
    });

    it('navigates to signin when switch to login is clicked from signup', () => {
      render(
        <TestWrapper>
          <MockAuthContainer initialView="signup" />
        </TestWrapper>
      );

      const switchButton = screen.getByTestId('switch-to-login');
      switchButton.click();

      expect(mockNavigate).toHaveBeenCalledWith('/signin');
    });

    it('navigates to signin when back to login is clicked from forgot password', () => {
      render(
        <TestWrapper>
          <MockAuthContainer initialView="forgot-password" />
        </TestWrapper>
      );

      const switchButton = screen.getByTestId('switch-to-login');
      switchButton.click();

      expect(mockNavigate).toHaveBeenCalledWith('/signin');
    });

    it('updates view when initialView prop changes', () => {
      const { rerender } = render(
        <TestWrapper>
          <MockAuthContainer initialView="login" />
        </TestWrapper>
      );

      expect(screen.getByTestId('login-form')).toBeInTheDocument();

      rerender(
        <TestWrapper>
          <MockAuthContainer initialView="signup" />
        </TestWrapper>
      );

      expect(screen.getByTestId('signup-form')).toBeInTheDocument();
      expect(screen.queryByTestId('login-form')).not.toBeInTheDocument();
    });
  });

  describe('when user is authenticated', () => {
    beforeEach(() => {
      mockUseAuth.mockReturnValue({
        user: {
          id: '1',
          name: 'John Doe',
          email: '<EMAIL>'
        }
      });
    });

    it('renders user dashboard instead of auth forms', () => {
      render(
        <TestWrapper>
          <MockAuthContainer initialView="login" />
        </TestWrapper>
      );

      expect(screen.getByTestId('user-dashboard')).toBeInTheDocument();
      expect(screen.getByText('User Dashboard')).toBeInTheDocument();
      expect(screen.queryByTestId('login-form')).not.toBeInTheDocument();
      expect(screen.queryByTestId('signup-form')).not.toBeInTheDocument();
      expect(screen.queryByTestId('forgot-password-form')).not.toBeInTheDocument();
    });

    it('renders user dashboard regardless of initialView when authenticated', () => {
      render(
        <TestWrapper>
          <MockAuthContainer initialView="signup" />
        </TestWrapper>
      );

      expect(screen.getByTestId('user-dashboard')).toBeInTheDocument();
      expect(screen.queryByTestId('signup-form')).not.toBeInTheDocument();
    });
  });
});

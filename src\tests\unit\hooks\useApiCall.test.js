import { act, renderHook, waitFor } from '@testing-library/react';
import useApiCall from '@/hooks/useApiCall';

jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));
const { toast } = require('sonner');

describe('useApiCall', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('handles successful API call with success message and callback', async () => {
    const apiCall = jest.fn().mockResolvedValue('ok');
    const onSuccess = jest.fn();
    const { result } = renderHook(() => useApiCall());
    await act(async () => {
      const res = await result.current.execute(apiCall, {
        onSuccess,
        successMessage: 'Yay',
      });
      expect(res).toBe('ok');
    });
    expect(result.current.success).toBe(true);
    expect(toast.success).toHaveBeenCalledWith('Yay');
    expect(onSuccess).toHaveBeenCalledWith('ok');
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe('');
  });

  it('handles API error with error message, toast, and callback', async () => {
    const apiCall = jest.fn().mockRejectedValue(new Error('fail'));
    const onError = jest.fn();
    const { result } = renderHook(() => useApiCall());
    await expect(
      act(async () => {
        await result.current.execute(apiCall, {
          onError,
          errorMessage: 'Custom error',
        });
      })
    ).rejects.toThrow('fail');
    expect(toast.error).toHaveBeenCalledWith('Custom error');
    expect(onError).toHaveBeenCalled();
    expect(result.current.loading).toBe(false);
    expect(result.current.success).toBe(false);
  });

  it('prevents multiple simultaneous calls', async () => {
    const apiCall = jest.fn().mockResolvedValue('ok');
    const { result } = renderHook(() => useApiCall());
    let firstPromise;
    await act(async () => {
      firstPromise = result.current.execute(apiCall);
      await result.current.execute(apiCall);
      await firstPromise;
    });
    expect(apiCall).toHaveBeenCalledTimes(1);
  });

  it('resets state with reset', () => {
    const { result } = renderHook(() => useApiCall());
    act(() => {
      result.current.setError('err');
      result.current.setSuccess(true);
      result.current.reset();
    });
    expect(result.current.error).toBe('');
    expect(result.current.success).toBe(false);
    expect(result.current.loading).toBe(false);
  });

  it('does not show toast if showToast is false', async () => {
    const apiCall = jest.fn().mockResolvedValue('ok');
    const { result } = renderHook(() => useApiCall());
    await act(async () => {
      await result.current.execute(apiCall, { showToast: false, successMessage: 'Yay', errorMessage: 'Err' });
    });
    expect(toast.success).not.toHaveBeenCalled();
    expect(toast.error).not.toHaveBeenCalled();
  });

  it('does not reset state if resetOnStart is false', async () => {
    const apiCall = jest.fn().mockResolvedValue('ok');
    const { result } = renderHook(() => useApiCall());
    act(() => {
      result.current.setError('err');
      result.current.setSuccess(true);
    });
    await act(async () => {
      await result.current.execute(apiCall, { resetOnStart: false });
    });
    expect(result.current.error).toBe('err');
    expect(result.current.success).toBe(true);
  });
}); 